# Be sure to restart your server when you modify this file.
if ENV['MIRRAW_DOMAIN'].to_s == 'api.mirraw.com'
  Rails.application.config.session_store :redis_store, servers: "redis://127.0.0.1:6379/0/session", key: '_mirraw_mobile_session', expire_after: 30.days
else
  Rails.application.config.session_store :redis_store, servers: "redis://127.0.0.1:6379/0/session", key: '_mirraw_mobile_session', expire_after: 30.days
end
# Temp Fix Remove for API side
#, expire_after: 10.days
